import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controller/card_controller.dart';
import '../widget/gacha_animation_widget.dart';
import '../../wallet/controller/wallet_controller.dart';
import '../../../common/models/ai_role.dart';
import '../../../widgets/role_display_card.dart';

/// Role-specific gacha page
class RoleGachaPage extends GetView<CardController> {
  const RoleGachaPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final AiRole role = Get.arguments as AiRole;
    final walletController = Get.find<WalletController>();

    return Scaffold(
      backgroundColor: Colors.black,
      extendBodyBehindAppBar: true,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Get.back(),
        ),
        title: Text(
          '${role.name} Cards',
          style: const TextStyle(
            color: Colors.white,
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: true,
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            const SizedBox(height: 100), // AppBar spacing
            
            // Role banner
            RoleDisplayCard(
              role: role,
              mode: RoleDisplayMode.banner,
              height: 200,
              onTap: () {
                // 可以添加点击角色卡片的逻辑，比如查看角色详情
              },
            ),
            
            const SizedBox(height: 32),
            
            // Balance display
            _buildBalanceCard(walletController),
            
            const SizedBox(height: 32),
            
            // Gacha buttons
            _buildGachaButtons(role),
            
            const SizedBox(height: 32),
            
            // Rate information
            _buildRateInfo(),
            
            const SizedBox(height: 32),
          ],
        ),
      ),
    );
  }



  /// Build balance card
  Widget _buildBalanceCard(WalletController walletController) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[900],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.blue.withValues(alpha: 0.3)),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          const Text(
            'Balance',
            style: TextStyle(
              color: Colors.white,
              fontSize: 16,
            ),
          ),
          Obx(() => Text(
            '${walletController.currentBalance} 💎',
            style: const TextStyle(
              color: Colors.yellow,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          )),
        ],
      ),
    );
  }

  /// Build gacha buttons
  Widget _buildGachaButtons(AiRole role) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        children: [
          // Single pull
          _buildGachaButton(
            title: 'Single Pull',
            subtitle: 'Get 1 ${role.name} card',
            cost: 100,
            color: Colors.blue,
            onTap: () => _performRoleGacha(role, 1),
          ),
          
          const SizedBox(height: 16),
          
          // Ten pull
          _buildGachaButton(
            title: 'Ten Pull',
            subtitle: 'Get 10 ${role.name} cards',
            cost: 900,
            color: Colors.purple,
            onTap: () => _performRoleGacha(role, 10),
          ),
        ],
      ),
    );
  }

  /// Build gacha button
  Widget _buildGachaButton({
    required String title,
    required String subtitle,
    required int cost,
    required Color color,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.2),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: color),
        ),
        child: Column(
          children: [
            Text(
              title,
              style: TextStyle(
                color: color,
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              subtitle,
              style: const TextStyle(
                color: Colors.white70,
                fontSize: 14,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              '$cost 💎',
              style: const TextStyle(
                color: Colors.yellow,
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Build rate information
  Widget _buildRateInfo() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[900],
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Drop Rates',
            style: TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          _buildRateItem('Mythic', '0.2%', Colors.red),
          _buildRateItem('Legendary', '2.8%', Colors.orange),
          _buildRateItem('Epic', '12%', Colors.purple),
          _buildRateItem('Rare', '25%', Colors.blue),
          _buildRateItem('Common', '60%', Colors.grey),
        ],
      ),
    );
  }

  /// Build rate item
  Widget _buildRateItem(String rarity, String rate, Color color) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            rarity,
            style: TextStyle(
              color: color,
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
          ),
          Text(
            rate,
            style: const TextStyle(
              color: Colors.white70,
              fontSize: 14,
            ),
          ),
        ],
      ),
    );
  }

  /// Perform role-specific gacha
  void _performRoleGacha(AiRole role, int count) async {
    try {
      // For now, use the general gacha system
      // TODO: Implement role-specific gacha logic
      final result = count == 1
        ? await controller.performSinglePull()
        : await controller.performTenPull();

      // Show animation
      if (result != null) {
        Get.to(() => GachaAnimationWidget(result: result));
      }
    } catch (e) {
      Get.snackbar(
        'Error',
        e.toString(),
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }
}
